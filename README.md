# 小宝的照片集 - Aura Gallery

一个现代、优雅的响应式照片展示网站，采用 Aura Gradient & Glass 设计系统。

## ✨ 特性

- 🎨 **现代设计** - 采用玻璃拟态（Glassmorphism）和动态渐变背景
- 📱 **完全响应式** - 完美适配手机、平板和桌面设备
- ⚡ **丰富交互** - 悬停效果、触摸反馈、键盘快捷键
- 🖼️ **照片展示** - 网格布局、模态框查看、分页浏览
- 📥 **实用功能** - 图片下载、分享、收藏
- 🎯 **用户友好** - 直观的导航和操作体验

## 🚀 技术栈

- **HTML5** - 语义化结构
- **CSS3** - 现代样式和动画
- **JavaScript** - 原生JS，无依赖
- **GitHub Pages** - 免费托管

## 📱 功能特性

### 导航
- 桌面端顶部导航
- 移动端底部导航
- 键盘快捷键支持

### 照片浏览
- 响应式网格布局
- 点击放大查看
- 分页浏览
- 懒加载优化

### 交互体验
- 玻璃拟态效果
- 悬停动画
- 触摸反馈
- 涟漪效果

## 🎨 设计系统

采用 **Aura Gradient & Glass** 设计系统：

- **色彩** - 深邃的午夜蓝到紫罗兰色渐变
- **字体** - 现代几何无衬线字体
- **效果** - 玻璃拟态、动态渐变、柔和光晕
- **布局** - 黄金比例、充足留白

## 📦 部署

本项目已配置自动部署到 GitHub Pages：

1. Fork 或 Clone 此仓库
2. 在 GitHub 仓库设置中启用 Pages
3. 选择 GitHub Actions 作为部署源
4. 推送代码即可自动部署

## 🔧 本地开发

```bash
# 克隆仓库
git clone [your-repo-url]

# 进入目录
cd xiaobao-gallery

# 启动本地服务器
python -m http.server 8000
# 或使用 Node.js
npx serve .

# 访问 http://localhost:8000
```

## 📝 自定义

### 添加照片
将照片放入 `image/` 文件夹，并在 `script.js` 中的 `imageFiles` 数组中添加文件名。

### 修改样式
编辑 `style.css` 中的 CSS 变量来自定义颜色和样式。

### 更改内容
修改 `index.html` 中的文本内容和标题。

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

💝 用爱制作，记录美好时光
