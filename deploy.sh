#!/bin/bash

echo "========================================"
echo "    小宝照片集 - GitHub Pages 部署"
echo "========================================"
echo

# 检查是否已初始化 Git
if [ ! -d ".git" ]; then
    echo "初始化 Git 仓库..."
    git init
    echo
fi

# 添加所有文件
echo "添加文件到 Git..."
git add .
echo

# 提交更改
read -p "请输入提交信息 (默认: Update gallery): " commit_msg
commit_msg=${commit_msg:-"Update gallery"}

echo "提交更改..."
git commit -m "$commit_msg"
echo

# 检查是否已添加远程仓库
if ! git remote get-url origin >/dev/null 2>&1; then
    echo
    echo "请先在 GitHub 上创建一个新仓库，然后输入仓库 URL:"
    echo "格式: https://github.com/你的用户名/仓库名.git"
    read -p "GitHub 仓库 URL: " repo_url
    
    echo "添加远程仓库..."
    git remote add origin "$repo_url"
    echo
fi

# 推送到 GitHub
echo "推送到 GitHub..."
git branch -M main
git push -u origin main

echo
echo "========================================"
echo "           部署完成！"
echo "========================================"
echo
echo "请按以下步骤启用 GitHub Pages:"
echo
echo "1. 访问你的 GitHub 仓库"
echo "2. 点击 Settings 选项卡"
echo "3. 在左侧菜单中找到 Pages"
echo "4. 在 Source 下选择 'GitHub Actions'"
echo "5. 等待几分钟，你的网站就会上线！"
echo
echo "网站地址将是: https://你的用户名.github.io/仓库名"
echo
