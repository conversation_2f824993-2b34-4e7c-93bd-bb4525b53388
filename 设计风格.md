{"designSystemProfile": {"name": "Aura Gradient & Glass", "version": "1.0", "description": "一个现代、流畅的设计系统，专为数字和金融科技产品设计。其核心特征是玻璃拟态（Glassmorphism）、动态渐变背景和柔和的辉光效果，旨在营造一种精致、未来感和有深度的用户体验。", "designPrinciples": {"overallVibe": "未来感、精致、流畅、有深度", "keyFeatures": ["Glassmorphism: UI元素（如卡片、面板）具有磨砂玻璃效果，通过背景模糊、半透明填充和精细的边框来实现。", "Fluid Gradients: 背景使用深色、多色的网格或径向渐变，色彩过渡平滑、自然，营造出光环或极光般的效果。", "Depth & Layering: 通过模糊、透明度和分层来创建清晰的视觉深度感。背景是最远的，装饰性图形居中，UI元素和文本在最顶层。", "Soft Glows & Lighting: 在背景和元素周围使用柔和的光晕效果，增强了氛围感和未来主义美学。", "Clean & Airy Layout: 布局开放，留白充足，结合清晰的排版层次，确保在视觉丰富的背景下仍有出色的可读性。"]}, "colorPalette": {"background": {"type": "<PERSON><PERSON>", "colors": ["#2A2A72", "#4C3A9A", "#8A2BE2"], "description": "由深邃的午夜蓝到紫罗兰色和品红色的平滑、动态渐变，构成了界面的主背景。"}, "primary": {"interactive": "#4A69FF", "description": "明亮的蓝色，用作主要的行动号召（CTA）按钮，以在深色背景上脱颖而出。"}, "text": {"heading": "#FFFFFF", "body": "#E0E0E0", "subtle": "#B0B0B0", "description": "标题使用纯白色以获得最大对比度。正文使用柔和的灰白色，以减少视觉疲劳。"}, "glass": {"fill": "rgba(255, 255, 255, 0.1)", "stroke": "rgba(255, 255, 255, 0.25)", "description": "用于玻璃拟态元素的颜色。半透明的白色填充和更不透明的白色边框来捕捉光线，定义边缘。"}}, "typography": {"family": "Geometric Sans-Serif", "description": "推荐使用现代、几何感的无衬线字体（例如 'Poppins', 'Inter', 'Circular Std'），以其清晰的字形和友好的感觉为特点。", "styles": {"heading1": {"fontWeight": "700", "size": "Extra Large", "color": "text.heading", "description": "用于页面的主要价值主张，字号最大，字重最粗。"}, "heading2": {"fontWeight": "500", "case": "UPPERCASE", "letterSpacing": "1.5px", "color": "text.subtle", "description": "用于次级标题或标签，通常采用大写和增加字间距的方式。"}, "body": {"fontWeight": "400", "size": "Medium", "color": "text.body", "lineHeight": "1.6", "description": "用于段落和描述性文本，注重可读性。"}, "link": {"fontWeight": "500", "color": "text.heading", "description": "用于导航链接或次要操作，通常与图标配对。"}}}, "layout": {"container": {"style": "Contained with rounded corners", "padding": "Generous (e.g., 48px to 64px)", "description": "主要内容被包含在一个大的圆角矩形内，与浏览器窗口边缘有一定距离。"}, "spacing": {"principle": "Whitespace-driven", "description": "大量使用负空间来分隔内容区域和元素，创造一个干净、不拥挤的界面。"}, "structure": ["Header: 简洁的顶部导航栏，左侧为Logo，中间为导航链接，右侧为次要CTA按钮。", "Hero Section: 采用两列布局，左侧为文本内容（标题、描述、CTA），右侧为主要的视觉图形元素。"]}, "effects": {"backgroundBlur": {"intensity": "Medium (e.g., 15px to 25px)", "description": "应用于玻璃拟态元素背后的内容，是实现该效果的关键。"}, "decorativeBlobs": {"style": "Soft, out-of-focus, colored spheres", "opacity": "0.5 to 0.8", "behavior": "Can be static or subtly animated (e.g., slow floating)", "description": "在背景和内容层之间漂浮的柔和色块，用于增加深度和视觉趣味。"}}, "components": {"buttonPrimary": {"shape": "<PERSON>ll", "style": "Solid Fill", "fill": "primary.interactive", "stroke": "none", "shadow": "Subtle glow effect using the same color", "typography": "font-weight: 600; color: #FFFFFF;"}, "buttonSecondary": {"shape": "<PERSON>ll", "style": "Glassmorphism", "fill": "glass.fill", "stroke": "1px solid glass.stroke", "effects": ["backgroundBlur"]}, "glassPanel": {"shape": "Rounded Rectangle (medium radius)", "style": "Glassmorphism", "fill": "glass.fill", "stroke": "1px solid glass.stroke", "effects": ["backgroundBlur"], "description": "用于展示内容卡片、仪表盘小部件等的核心组件。"}, "navBar": {"style": "Floating", "background": "transparent", "description": "导航栏本身是透明的，其链接和按钮直接浮动在主背景之上。"}}}}