// 全局变量
let photos = [];
let currentPage = 1;
let photosPerPage = 12;
let currentView = 'grid';
let currentModalIndex = 0;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadPhotos();
    initializeNavigation();
    initializeGalleryControls();
    initializePagination();
    initializeModal();
    updatePhotoCount();
    initializeKeyboardShortcuts();
    initializeIntersectionObserver();
    addEnhancedInteractions();
});

// 初始化键盘快捷键
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // 只在没有输入框聚焦时响应快捷键
        if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
            return;
        }

        switch (e.key) {
            case '1':
                showSection('home');
                break;
            case '2':
                showSection('gallery');
                break;
            case '3':
                showSection('about');
                break;
            case 'g':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    showSection('gallery');
                }
                break;
            case 'h':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    showSection('home');
                }
                break;
            case 'ArrowLeft':
                if (document.querySelector('.section.active').id === 'gallery') {
                    e.preventDefault();
                    navigatePage(-1);
                }
                break;
            case 'ArrowRight':
                if (document.querySelector('.section.active').id === 'gallery') {
                    e.preventDefault();
                    navigatePage(1);
                }
                break;
            case ' ':
                if (document.querySelector('.section.active').id === 'gallery') {
                    e.preventDefault();
                    navigatePage(1);
                }
                break;
        }
    });
}

// 初始化交叉观察器（用于懒加载和动画）
function initializeIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    // 观察所有需要动画的元素
    document.querySelectorAll('.photo-item, .glass-panel, .section').forEach(el => {
        observer.observe(el);
    });
}

// 添加增强交互
function addEnhancedInteractions() {
    // 为所有按钮添加触摸反馈
    document.querySelectorAll('button, .nav-link, .bottom-nav-item').forEach(element => {
        element.classList.add('touch-feedback');
    });

    // 添加磁性效果
    document.querySelectorAll('.photo-item, .cta-button').forEach(element => {
        element.classList.add('magnetic');
    });

    // 添加脉冲边框效果到活跃元素
    document.querySelectorAll('.nav-link.active, .bottom-nav-item.active').forEach(element => {
        element.classList.add('pulse-border');
    });
}

// 页面导航
function navigatePage(direction) {
    const totalPages = Math.ceil(photos.length / photosPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        renderPhotos();
        updatePagination();
        scrollToTop();

        // 添加页面切换动画
        const photoGrid = document.getElementById('photo-grid');
        photoGrid.style.opacity = '0';
        photoGrid.style.transform = 'translateY(20px)';

        setTimeout(() => {
            photoGrid.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            photoGrid.style.opacity = '1';
            photoGrid.style.transform = 'translateY(0)';
        }, 100);
    }
}

// 加载照片数据
async function loadPhotos() {
    try {
        // 获取 image 文件夹中的所有照片
        const imageFiles = [
            '微信图片_20250628213135.jpg',
            '微信图片_20250629164641.jpg',
            '微信图片_20250629164648.jpg',
            '微信图片_20250701154224.jpg',
            '微信图片_20250704183626.jpg',
            '微信图片_20250704194141.jpg',
            '微信图片_20250704194143.jpg',
            '微信图片_20250704194145.jpg',
            '微信图片_20250704194149.jpg',
            '微信图片_20250704194151.jpg',
            '微信图片_20250704194155.jpg',
            '微信图片_20250704194159.jpg',
            '微信图片_20250704194203.jpg',
            '微信图片_20250704194208.jpg',
            '微信图片_20250704194213.jpg',
            '微信图片_20250704194214.jpg',
            '微信图片_20250704194215.jpg',
            '微信图片_202507041942151.jpg',
            '微信图片_20250704194216.jpg',
            '微信图片_20250704194217.jpg',
            '微信图片_202507041942171.jpg',
            '微信图片_20250704194218.jpg',
            '微信图片_202507041942181.jpg',
            '微信图片_20250704194219.jpg',
            '微信图片_20250704194220.jpg',
            '微信图片_202507041942201.jpg',
            '微信图片_20250704194221.jpg',
            '微信图片_202507041942211.jpg',
            '微信图片_20250704194224.jpg',
            '微信图片_20250704194228.jpg',
            '微信图片_20250704194230.jpg',
            '微信图片_20250704194234.jpg',
            '微信图片_20250704194237.jpg',
            '微信图片_20250704194238.jpg',
            '微信图片_20250704194242.jpg',
            '微信图片_20250704194245.jpg',
            '微信图片_20250704194246.jpg',
            '微信图片_20250704194248.jpg',
            '微信图片_20250704194249.jpg',
            '微信图片_20250704194354.jpg',
            '微信图片_20250704194356.jpg',
            '微信图片_20250704194357.jpg',
            '微信图片_202507041943571.jpg',
            '微信图片_20250704194358.jpg',
            '微信图片_202507041943581.jpg',
            '微信图片_202507041943582.jpg',
            '微信图片_20250704194359.jpg',
            '微信图片_202507041943591.jpg',
            '微信图片_202507041943592.jpg',
            '微信图片_20250704194400.jpg',
            '微信图片_202507041944001.jpg',
            '微信图片_202507041944002.jpg',
            '微信图片_202507041944003.jpg',
            '微信图片_20250704194401.jpg',
            '微信图片_202507041944011.jpg',
            '微信图片_202507041944012.jpg',
            '微信图片_202507041944013.jpg',
            '微信图片_20250704194402.jpg',
            '微信图片_202507041944021.jpg'
        ];

        photos = imageFiles.map((filename, index) => ({
            id: index + 1,
            src: `image/${filename}`,
            title: `照片 ${index + 1}`,
            filename: filename
        }));

        renderPhotos();
        updatePagination();
        loadPreviewImages();
    } catch (error) {
        console.error('加载照片失败:', error);
    }
}

// 渲染照片网格
function renderPhotos() {
    const photoGrid = document.getElementById('photo-grid');
    const startIndex = (currentPage - 1) * photosPerPage;
    const endIndex = startIndex + photosPerPage;
    const currentPhotos = photos.slice(startIndex, endIndex);

    photoGrid.innerHTML = '';
    photoGrid.className = `photo-grid ${currentView}`;

    currentPhotos.forEach((photo, index) => {
        const photoItem = document.createElement('div');
        photoItem.className = 'photo-item touch-feedback';
        photoItem.style.animationDelay = `${index * 0.1}s`;

        photoItem.innerHTML = `
            <img src="${photo.src}" alt="${photo.title}" loading="lazy" onload="handleImageLoad(this)" onerror="handleImageError(this)">
            <div class="photo-actions">
                <button class="photo-action-btn" onclick="event.stopPropagation(); downloadImage('${photo.src}', '${photo.title}')" title="下载">
                    <i class="fas fa-download"></i>
                </button>
                <button class="photo-action-btn" onclick="event.stopPropagation(); shareImage('${photo.src}', '${photo.title}')" title="分享">
                    <i class="fas fa-share"></i>
                </button>
            </div>
            <div class="photo-overlay">
                <div class="photo-title">${photo.title}</div>
                <div class="photo-date">${formatDate(photo.filename)}</div>
            </div>
        `;

        // 添加触摸反馈和高级交互
        photoItem.addEventListener('touchstart', handleTouchStart);
        photoItem.addEventListener('touchend', handleTouchEnd);

        // 双击放大
        let clickCount = 0;
        photoItem.addEventListener('click', (e) => {
            clickCount++;
            if (clickCount === 1) {
                setTimeout(() => {
                    if (clickCount === 1) {
                        openModal(startIndex + index);
                    }
                    clickCount = 0;
                }, 300);
            } else if (clickCount === 2) {
                // 双击效果
                photoItem.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    photoItem.style.transform = '';
                    openModal(startIndex + index);
                }, 200);
                clickCount = 0;
            }
        });

        // 长按菜单
        let longPressTimer;
        photoItem.addEventListener('mousedown', (e) => {
            longPressTimer = setTimeout(() => {
                showContextMenu(e, photo, startIndex + index);
            }, 800);
        });

        photoItem.addEventListener('mouseup', () => {
            clearTimeout(longPressTimer);
        });

        photoItem.addEventListener('mouseleave', () => {
            clearTimeout(longPressTimer);
        });

        // 触摸长按
        photoItem.addEventListener('touchstart', (e) => {
            longPressTimer = setTimeout(() => {
                navigator.vibrate && navigator.vibrate(50); // 触觉反馈
                showContextMenu(e.touches[0], photo, startIndex + index);
            }, 800);
        });

        photoItem.addEventListener('touchend', () => {
            clearTimeout(longPressTimer);
        });

        photoItem.addEventListener('touchmove', () => {
            clearTimeout(longPressTimer);
        });

        photoGrid.appendChild(photoItem);
    });
}

// 处理图片加载成功
function handleImageLoad(img) {
    img.parentElement.classList.remove('loading');
    img.style.opacity = '0';
    setTimeout(() => {
        img.style.transition = 'opacity 0.3s ease';
        img.style.opacity = '1';
    }, 100);
}

// 处理图片加载失败
function handleImageError(img) {
    img.parentElement.classList.remove('loading');
    img.style.display = 'none';
    const placeholder = document.createElement('div');
    placeholder.className = 'image-placeholder';
    placeholder.innerHTML = '<i class="fas fa-image"></i><span>图片加载失败</span>';
    img.parentElement.appendChild(placeholder);
}

// 下载图片
function downloadImage(src, title) {
    const link = document.createElement('a');
    link.href = src;
    link.download = title;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 显示下载提示
    showToast('开始下载图片');
}

// 分享图片
function shareImage(src, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            text: `查看这张美丽的照片：${title}`,
            url: window.location.href
        }).catch(console.error);
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            showToast('链接已复制到剪贴板');
        }).catch(() => {
            showToast('分享功能不可用');
        });
    }
}

// 格式化日期
function formatDate(filename) {
    const match = filename.match(/(\d{8})/);
    if (match) {
        const dateStr = match[1];
        const year = dateStr.substring(0, 4);
        const month = dateStr.substring(4, 6);
        const day = dateStr.substring(6, 8);
        return `${year}-${month}-${day}`;
    }
    return '未知日期';
}

// 显示提示消息
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--glass-fill);
        backdrop-filter: blur(15px);
        color: var(--text-heading);
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid var(--glass-stroke);
        z-index: 3000;
        animation: slideInRight 0.3s ease;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 显示上下文菜单
function showContextMenu(e, photo, index) {
    // 移除现有菜单
    const existingMenu = document.querySelector('.context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }

    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.style.cssText = `
        position: fixed;
        top: ${e.clientY || e.pageY}px;
        left: ${e.clientX || e.pageX}px;
        background: var(--glass-fill);
        backdrop-filter: blur(15px);
        border: 1px solid var(--glass-stroke);
        border-radius: 12px;
        padding: 8px;
        z-index: 3000;
        min-width: 150px;
        animation: contextMenuSlideIn 0.2s ease;
    `;

    const menuItems = [
        { icon: 'fas fa-eye', text: '查看大图', action: () => openModal(index) },
        { icon: 'fas fa-download', text: '下载图片', action: () => downloadImage(photo.src, photo.title) },
        { icon: 'fas fa-share', text: '分享图片', action: () => shareImage(photo.src, photo.title) },
        { icon: 'fas fa-info-circle', text: '图片信息', action: () => showImageInfo(photo) },
        { icon: 'fas fa-heart', text: '收藏', action: () => toggleFavorite(photo) }
    ];

    menuItems.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.className = 'context-menu-item';
        menuItem.innerHTML = `<i class="${item.icon}"></i> ${item.text}`;
        menuItem.style.cssText = `
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--text-body);
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s ease;
        `;

        menuItem.addEventListener('mouseenter', () => {
            menuItem.style.background = 'var(--glass-hover)';
            menuItem.style.color = 'var(--text-heading)';
        });

        menuItem.addEventListener('mouseleave', () => {
            menuItem.style.background = 'transparent';
            menuItem.style.color = 'var(--text-body)';
        });

        menuItem.addEventListener('click', () => {
            item.action();
            menu.remove();
        });

        menu.appendChild(menuItem);
    });

    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    setTimeout(() => {
        document.addEventListener('click', function closeMenu() {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        });
    }, 100);
}

// 显示图片信息
function showImageInfo(photo) {
    const info = `
        <div class="image-info-modal">
            <h3>${photo.title}</h3>
            <p><strong>文件名:</strong> ${photo.filename}</p>
            <p><strong>日期:</strong> ${formatDate(photo.filename)}</p>
            <p><strong>编号:</strong> ${photo.id}</p>
        </div>
    `;

    showModal('图片信息', info);
}

// 切换收藏状态
function toggleFavorite(photo) {
    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    const index = favorites.findIndex(fav => fav.id === photo.id);

    if (index > -1) {
        favorites.splice(index, 1);
        showToast('已取消收藏');
    } else {
        favorites.push(photo);
        showToast('已添加到收藏');
    }

    localStorage.setItem('favorites', JSON.stringify(favorites));
}

// 显示通用模态框
function showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'info-modal';
    modal.innerHTML = `
        <div class="info-modal-backdrop"></div>
        <div class="info-modal-content">
            <div class="info-modal-header">
                <h3>${title}</h3>
                <button class="info-modal-close">&times;</button>
            </div>
            <div class="info-modal-body">${content}</div>
        </div>
    `;

    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    document.body.appendChild(modal);

    modal.querySelector('.info-modal-close').addEventListener('click', () => {
        modal.remove();
    });

    modal.querySelector('.info-modal-backdrop').addEventListener('click', () => {
        modal.remove();
    });
}

// 创建涟漪效果
function createRippleEffect(element, event) {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = (event?.clientX || rect.left + rect.width / 2) - rect.left - size / 2;
    const y = (event?.clientY || rect.top + rect.height / 2) - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, transparent 70%);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 10;
    `;

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// 触摸处理
function handleTouchStart(e) {
    this.style.transform = 'scale(0.95)';
    this.style.transition = 'transform 0.1s ease';
}

function handleTouchEnd(e) {
    this.style.transform = '';
    this.style.transition = 'transform 0.3s ease';
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        from {
            transform: scale(0);
            opacity: 1;
        }
        to {
            transform: scale(2);
            opacity: 0;
        }
    }

    @keyframes contextMenuSlideIn {
        from {
            opacity: 0;
            transform: scale(0.8) translateY(-10px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .info-modal-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .info-modal-content {
        background: var(--glass-fill);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-stroke);
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        animation: modalSlideIn 0.3s ease;
    }

    .info-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid var(--glass-stroke);
    }

    .info-modal-header h3 {
        margin: 0;
        color: var(--text-heading);
    }

    .info-modal-close {
        background: none;
        border: none;
        color: var(--text-body);
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .info-modal-close:hover {
        background: var(--glass-hover);
        color: var(--text-heading);
    }

    .info-modal-body {
        padding: 20px;
        color: var(--text-body);
    }

    .image-info-modal p {
        margin: 8px 0;
    }
`;
document.head.appendChild(style);

// 初始化首页封面
function initializeHeroCover() {
    const heroImageContainer = document.querySelector('.hero-image-container');
    if (heroImageContainer) {
        // 添加点击事件，点击封面图片直接进入相册
        heroImageContainer.addEventListener('click', () => {
            // 创建涟漪效果
            createRippleEffect(heroImageContainer, event);

            // 添加点击动画效果
            heroImageContainer.style.transform = 'scale(0.95)';
            heroImageContainer.style.filter = 'brightness(1.2)';

            setTimeout(() => {
                heroImageContainer.style.transform = '';
                heroImageContainer.style.filter = '';
                showSection('gallery');
            }, 200);
        });

        // 添加触摸反馈
        heroImageContainer.classList.add('touch-feedback');

        // 添加长按显示图片信息
        let longPressTimer;
        heroImageContainer.addEventListener('mousedown', (e) => {
            longPressTimer = setTimeout(() => {
                const heroPhoto = {
                    src: 'image/微信图片_202507041943571.jpg',
                    title: '首页封面照片',
                    filename: '微信图片_202507041943571.jpg',
                    id: 'hero'
                };
                showImageInfo(heroPhoto);
            }, 800);
        });

        heroImageContainer.addEventListener('mouseup', () => {
            clearTimeout(longPressTimer);
        });

        heroImageContainer.addEventListener('mouseleave', () => {
            clearTimeout(longPressTimer);
        });
    }
}

// 加载预览图片到首页（保留兼容性）
function loadPreviewImages() {
    // 初始化首页封面
    initializeHeroCover();

    // 如果还有其他预览元素，可以在这里处理
    const previewItems = document.querySelectorAll('.preview-item');
    const previewPhotos = photos.slice(0, 4);

    previewItems.forEach((item, index) => {
        if (previewPhotos[index]) {
            item.style.backgroundImage = `url(${previewPhotos[index].src})`;
            item.style.backgroundSize = 'cover';
            item.style.backgroundPosition = 'center';
        }
    });
}

// 导航功能
function initializeNavigation() {
    // 桌面端导航
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.dataset.section;
            showSection(section);
            updateActiveNav(link);
        });
    });

    // 移动端底部导航
    const bottomNavItems = document.querySelectorAll('.bottom-nav-item');
    bottomNavItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const section = item.dataset.section;
            showSection(section);
            updateActiveBottomNav(item);
        });
    });
}

// 显示指定部分
function showSection(sectionName) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });

    // 显示目标部分
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // 更新导航状态
    updateNavigationState(sectionName);
}

// 更新导航状态
function updateNavigationState(sectionName) {
    // 更新桌面端导航
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.toggle('active', link.dataset.section === sectionName);
    });

    // 更新移动端导航
    document.querySelectorAll('.bottom-nav-item').forEach(item => {
        item.classList.toggle('active', item.dataset.section === sectionName);
    });
}

// 更新活跃导航链接
function updateActiveNav(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

// 更新活跃底部导航
function updateActiveBottomNav(activeItem) {
    document.querySelectorAll('.bottom-nav-item').forEach(item => {
        item.classList.remove('active');
    });
    activeItem.classList.add('active');
}

// 初始化相册控制
function initializeGalleryControls() {
    // 视图切换
    const toggleBtns = document.querySelectorAll('.toggle-btn');
    toggleBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            toggleBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentView = btn.dataset.view;
            renderPhotos();
        });
    });
}

// 初始化分页
function initializePagination() {
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');

    prevBtn.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderPhotos();
            updatePagination();
            scrollToTop();
        }
    });

    nextBtn.addEventListener('click', () => {
        const totalPages = Math.ceil(photos.length / photosPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderPhotos();
            updatePagination();
            scrollToTop();
        }
    });
}

// 更新分页状态
function updatePagination() {
    const totalPages = Math.ceil(photos.length / photosPerPage);
    const currentPageSpan = document.getElementById('current-page');
    const totalPagesSpan = document.getElementById('total-pages');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const paginationDots = document.getElementById('pagination-dots');

    currentPageSpan.textContent = currentPage;
    totalPagesSpan.textContent = totalPages;

    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages;

    // 创建分页点
    paginationDots.innerHTML = '';
    for (let i = 1; i <= Math.min(totalPages, 5); i++) {
        const dot = document.createElement('div');
        dot.className = `pagination-dot ${i === currentPage ? 'active' : ''}`;
        dot.addEventListener('click', () => {
            currentPage = i;
            renderPhotos();
            updatePagination();
            scrollToTop();
        });
        paginationDots.appendChild(dot);
    }
}

// 滚动到顶部
function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 初始化模态框
function initializeModal() {
    const modal = document.getElementById('photo-modal');
    const backdrop = document.querySelector('.modal-backdrop');

    backdrop.addEventListener('click', closeModal);

    // 键盘导航
    document.addEventListener('keydown', (e) => {
        if (modal.classList.contains('active')) {
            switch (e.key) {
                case 'Escape':
                    closeModal();
                    break;
                case 'ArrowLeft':
                    navigateModal(-1);
                    break;
                case 'ArrowRight':
                    navigateModal(1);
                    break;
            }
        }
    });
}

// 打开模态框
function openModal(photoIndex) {
    currentModalIndex = photoIndex;
    const modal = document.getElementById('photo-modal');
    const modalImage = document.getElementById('modal-image');
    const modalTitle = document.getElementById('modal-title');
    const modalCurrent = document.getElementById('modal-current');
    const modalTotal = document.getElementById('modal-total');

    const photo = photos[photoIndex];
    modalImage.src = photo.src;
    modalImage.alt = photo.title;
    modalTitle.textContent = photo.title;
    modalCurrent.textContent = photoIndex + 1;
    modalTotal.textContent = photos.length;

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('photo-modal');
    modal.classList.remove('active');
    document.body.style.overflow = '';
}

// 模态框导航
function navigateModal(direction) {
    const newIndex = currentModalIndex + direction;
    if (newIndex >= 0 && newIndex < photos.length) {
        openModal(newIndex);
    }
}

// 更新照片计数
function updatePhotoCount() {
    setTimeout(() => {
        const totalPhotosElement = document.getElementById('total-photos');
        if (totalPhotosElement) {
            totalPhotosElement.textContent = photos.length;
        }
    }, 1000);
}

// 响应式处理
function handleResize() {
    const isMobile = window.innerWidth <= 768;
    photosPerPage = isMobile ? 8 : 12;
    
    if (currentPage > Math.ceil(photos.length / photosPerPage)) {
        currentPage = Math.ceil(photos.length / photosPerPage);
    }
    
    renderPhotos();
    updatePagination();
}

window.addEventListener('resize', handleResize);

// 平滑滚动
function smoothScroll(target) {
    document.querySelector(target).scrollIntoView({
        behavior: 'smooth'
    });
}

// 图片懒加载错误处理
document.addEventListener('error', (e) => {
    if (e.target.tagName === 'IMG') {
        e.target.style.display = 'none';
        console.warn('图片加载失败:', e.target.src);
    }
}, true);

// 触摸手势支持 (移动端)
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
});

document.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const modal = document.getElementById('photo-modal');
    if (modal.classList.contains('active')) {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                navigateModal(1); // 向左滑动，下一张
            } else {
                navigateModal(-1); // 向右滑动，上一张
            }
        }
    }
}
