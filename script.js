// 全局变量
let photos = [];
let currentPage = 1;
let photosPerPage = 12;
let currentView = 'grid';
let currentModalIndex = 0;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadPhotos();
    initializeNavigation();
    initializeGalleryControls();
    initializePagination();
    initializeModal();
    updatePhotoCount();
});

// 加载照片数据
async function loadPhotos() {
    try {
        // 获取 image 文件夹中的所有照片
        const imageFiles = [
            '微信图片_20250628213135.jpg',
            '微信图片_20250629164641.jpg',
            '微信图片_20250629164648.jpg',
            '微信图片_20250701154224.jpg',
            '微信图片_20250704183626.jpg',
            '微信图片_20250704194141.jpg',
            '微信图片_20250704194143.jpg',
            '微信图片_20250704194145.jpg',
            '微信图片_20250704194149.jpg',
            '微信图片_20250704194151.jpg',
            '微信图片_20250704194155.jpg',
            '微信图片_20250704194159.jpg',
            '微信图片_20250704194203.jpg',
            '微信图片_20250704194208.jpg',
            '微信图片_20250704194213.jpg',
            '微信图片_20250704194214.jpg',
            '微信图片_20250704194215.jpg',
            '微信图片_202507041942151.jpg',
            '微信图片_20250704194216.jpg',
            '微信图片_20250704194217.jpg',
            '微信图片_202507041942171.jpg',
            '微信图片_20250704194218.jpg',
            '微信图片_202507041942181.jpg',
            '微信图片_20250704194219.jpg',
            '微信图片_20250704194220.jpg',
            '微信图片_202507041942201.jpg',
            '微信图片_20250704194221.jpg',
            '微信图片_202507041942211.jpg',
            '微信图片_20250704194224.jpg',
            '微信图片_20250704194228.jpg',
            '微信图片_20250704194230.jpg',
            '微信图片_20250704194234.jpg',
            '微信图片_20250704194237.jpg',
            '微信图片_20250704194238.jpg',
            '微信图片_20250704194242.jpg',
            '微信图片_20250704194245.jpg',
            '微信图片_20250704194246.jpg',
            '微信图片_20250704194248.jpg',
            '微信图片_20250704194249.jpg',
            '微信图片_20250704194354.jpg',
            '微信图片_20250704194356.jpg',
            '微信图片_20250704194357.jpg',
            '微信图片_202507041943571.jpg',
            '微信图片_20250704194358.jpg',
            '微信图片_202507041943581.jpg',
            '微信图片_202507041943582.jpg',
            '微信图片_20250704194359.jpg',
            '微信图片_202507041943591.jpg',
            '微信图片_202507041943592.jpg',
            '微信图片_20250704194400.jpg',
            '微信图片_202507041944001.jpg',
            '微信图片_202507041944002.jpg',
            '微信图片_202507041944003.jpg',
            '微信图片_20250704194401.jpg',
            '微信图片_202507041944011.jpg',
            '微信图片_202507041944012.jpg',
            '微信图片_202507041944013.jpg',
            '微信图片_20250704194402.jpg',
            '微信图片_202507041944021.jpg'
        ];

        photos = imageFiles.map((filename, index) => ({
            id: index + 1,
            src: `image/${filename}`,
            title: `照片 ${index + 1}`,
            filename: filename
        }));

        renderPhotos();
        updatePagination();
        loadPreviewImages();
    } catch (error) {
        console.error('加载照片失败:', error);
    }
}

// 渲染照片网格
function renderPhotos() {
    const photoGrid = document.getElementById('photo-grid');
    const startIndex = (currentPage - 1) * photosPerPage;
    const endIndex = startIndex + photosPerPage;
    const currentPhotos = photos.slice(startIndex, endIndex);

    photoGrid.innerHTML = '';
    photoGrid.className = `photo-grid ${currentView}`;

    currentPhotos.forEach((photo, index) => {
        const photoItem = document.createElement('div');
        photoItem.className = 'photo-item';
        photoItem.style.animationDelay = `${index * 0.1}s`;
        
        photoItem.innerHTML = `
            <img src="${photo.src}" alt="${photo.title}" loading="lazy">
            <div class="photo-overlay">
                <div class="photo-title">${photo.title}</div>
            </div>
        `;

        photoItem.addEventListener('click', () => {
            openModal(startIndex + index);
        });

        photoGrid.appendChild(photoItem);
    });
}

// 加载预览图片到首页
function loadPreviewImages() {
    const previewItems = document.querySelectorAll('.preview-item');
    const previewPhotos = photos.slice(0, 4);
    
    previewItems.forEach((item, index) => {
        if (previewPhotos[index]) {
            item.style.backgroundImage = `url(${previewPhotos[index].src})`;
            item.style.backgroundSize = 'cover';
            item.style.backgroundPosition = 'center';
        }
    });
}

// 导航功能
function initializeNavigation() {
    // 桌面端导航
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.dataset.section;
            showSection(section);
            updateActiveNav(link);
        });
    });

    // 移动端底部导航
    const bottomNavItems = document.querySelectorAll('.bottom-nav-item');
    bottomNavItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const section = item.dataset.section;
            showSection(section);
            updateActiveBottomNav(item);
        });
    });
}

// 显示指定部分
function showSection(sectionName) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });

    // 显示目标部分
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // 更新导航状态
    updateNavigationState(sectionName);
}

// 更新导航状态
function updateNavigationState(sectionName) {
    // 更新桌面端导航
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.toggle('active', link.dataset.section === sectionName);
    });

    // 更新移动端导航
    document.querySelectorAll('.bottom-nav-item').forEach(item => {
        item.classList.toggle('active', item.dataset.section === sectionName);
    });
}

// 更新活跃导航链接
function updateActiveNav(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

// 更新活跃底部导航
function updateActiveBottomNav(activeItem) {
    document.querySelectorAll('.bottom-nav-item').forEach(item => {
        item.classList.remove('active');
    });
    activeItem.classList.add('active');
}

// 初始化相册控制
function initializeGalleryControls() {
    // 视图切换
    const toggleBtns = document.querySelectorAll('.toggle-btn');
    toggleBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            toggleBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentView = btn.dataset.view;
            renderPhotos();
        });
    });
}

// 初始化分页
function initializePagination() {
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');

    prevBtn.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderPhotos();
            updatePagination();
            scrollToTop();
        }
    });

    nextBtn.addEventListener('click', () => {
        const totalPages = Math.ceil(photos.length / photosPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderPhotos();
            updatePagination();
            scrollToTop();
        }
    });
}

// 更新分页状态
function updatePagination() {
    const totalPages = Math.ceil(photos.length / photosPerPage);
    const currentPageSpan = document.getElementById('current-page');
    const totalPagesSpan = document.getElementById('total-pages');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const paginationDots = document.getElementById('pagination-dots');

    currentPageSpan.textContent = currentPage;
    totalPagesSpan.textContent = totalPages;

    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages;

    // 创建分页点
    paginationDots.innerHTML = '';
    for (let i = 1; i <= Math.min(totalPages, 5); i++) {
        const dot = document.createElement('div');
        dot.className = `pagination-dot ${i === currentPage ? 'active' : ''}`;
        dot.addEventListener('click', () => {
            currentPage = i;
            renderPhotos();
            updatePagination();
            scrollToTop();
        });
        paginationDots.appendChild(dot);
    }
}

// 滚动到顶部
function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 初始化模态框
function initializeModal() {
    const modal = document.getElementById('photo-modal');
    const backdrop = document.querySelector('.modal-backdrop');

    backdrop.addEventListener('click', closeModal);

    // 键盘导航
    document.addEventListener('keydown', (e) => {
        if (modal.classList.contains('active')) {
            switch (e.key) {
                case 'Escape':
                    closeModal();
                    break;
                case 'ArrowLeft':
                    navigateModal(-1);
                    break;
                case 'ArrowRight':
                    navigateModal(1);
                    break;
            }
        }
    });
}

// 打开模态框
function openModal(photoIndex) {
    currentModalIndex = photoIndex;
    const modal = document.getElementById('photo-modal');
    const modalImage = document.getElementById('modal-image');
    const modalTitle = document.getElementById('modal-title');
    const modalCurrent = document.getElementById('modal-current');
    const modalTotal = document.getElementById('modal-total');

    const photo = photos[photoIndex];
    modalImage.src = photo.src;
    modalImage.alt = photo.title;
    modalTitle.textContent = photo.title;
    modalCurrent.textContent = photoIndex + 1;
    modalTotal.textContent = photos.length;

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('photo-modal');
    modal.classList.remove('active');
    document.body.style.overflow = '';
}

// 模态框导航
function navigateModal(direction) {
    const newIndex = currentModalIndex + direction;
    if (newIndex >= 0 && newIndex < photos.length) {
        openModal(newIndex);
    }
}

// 更新照片计数
function updatePhotoCount() {
    setTimeout(() => {
        const totalPhotosElement = document.getElementById('total-photos');
        if (totalPhotosElement) {
            totalPhotosElement.textContent = photos.length;
        }
    }, 1000);
}

// 响应式处理
function handleResize() {
    const isMobile = window.innerWidth <= 768;
    photosPerPage = isMobile ? 8 : 12;
    
    if (currentPage > Math.ceil(photos.length / photosPerPage)) {
        currentPage = Math.ceil(photos.length / photosPerPage);
    }
    
    renderPhotos();
    updatePagination();
}

window.addEventListener('resize', handleResize);

// 平滑滚动
function smoothScroll(target) {
    document.querySelector(target).scrollIntoView({
        behavior: 'smooth'
    });
}

// 图片懒加载错误处理
document.addEventListener('error', (e) => {
    if (e.target.tagName === 'IMG') {
        e.target.style.display = 'none';
        console.warn('图片加载失败:', e.target.src);
    }
}, true);

// 触摸手势支持 (移动端)
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
});

document.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const modal = document.getElementById('photo-modal');
    if (modal.classList.contains('active')) {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                navigateModal(1); // 向左滑动，下一张
            } else {
                navigateModal(-1); // 向右滑动，上一张
            }
        }
    }
}
