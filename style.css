/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色变量 */
    --bg-gradient-start: #2A2A72;
    --bg-gradient-mid: #4C3A9A;
    --bg-gradient-end: #8A2BE2;
    --primary-interactive: #4A69FF;
    --text-heading: #FFFFFF;
    --text-body: #E0E0E0;
    --text-subtle: #B0B0B0;
    --glass-fill: rgba(255, 255, 255, 0.1);
    --glass-stroke: rgba(255, 255, 255, 0.25);
    --glass-hover: rgba(255, 255, 255, 0.15);
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    
    /* 间距 */
    --container-padding: clamp(24px, 5vw, 64px);
    --section-gap: clamp(32px, 8vw, 80px);
    
    /* 动画 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

body {
    font-family: var(--font-family);
    color: var(--text-body);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 动态背景 */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.gradient-mesh {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-mid) 50%, var(--bg-gradient-end) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.decorative-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: float 20s ease-in-out infinite;
}

.blob-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #FF6B9D, #4ECDC4);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.blob-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #A8E6CF, #FFD93D);
    top: 60%;
    right: 15%;
    animation-delay: -7s;
}

.blob-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, #FF8A80, #CE93D8);
    bottom: 20%;
    left: 20%;
    animation-delay: -14s;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* 玻璃拟态效果 */
.glass-panel {
    background: var(--glass-fill);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-stroke);
    border-radius: 20px;
    transition: var(--transition-smooth);
}

.glass-panel:hover {
    background: var(--glass-hover);
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 20px var(--container-padding);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-heading);
}

.nav-logo i {
    font-size: 1.5rem;
    color: var(--primary-interactive);
}

.nav-links {
    display: flex;
    gap: 32px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--text-body);
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 16px;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-interactive), #6B73FF);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 0.1;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-heading);
    background: var(--glass-fill);
    backdrop-filter: blur(15px);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.nav-link:active {
    transform: translateY(0);
}

.nav-link i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}

.nav-link.active i {
    color: var(--primary-interactive);
}

/* 主容器 */
.main-container {
    padding-top: 100px;
    padding-bottom: 100px;
    min-height: 100vh;
}

.section {
    display: none;
    padding: var(--section-gap) var(--container-padding);
    max-width: 1200px;
    margin: 0 auto;
}

.section.active {
    display: block;
}

/* 首页样式 */
.hero-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    width: 100%;
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 700;
    color: var(--text-heading);
    margin-bottom: 16px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-subtle);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 24px;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 32px;
    max-width: 500px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary-interactive), #6B73FF);
    color: var(--text-heading);
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    box-shadow: 0 8px 32px rgba(74, 105, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 45px rgba(74, 105, 255, 0.5);
}

.cta-button:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 5px 20px rgba(74, 105, 255, 0.4);
}

.cta-button i {
    transition: transform 0.3s ease;
}

.cta-button:hover i {
    transform: translateX(4px);
}

.hero-panel {
    padding: 20px;
    height: 450px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.hero-image-container:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
    filter: brightness(0.9);
}

.hero-image-container:hover .hero-image {
    filter: brightness(1.1);
    transform: scale(1.05);
}

/* 移除了首页封面的悬浮层，保持简洁风格 */

/* 添加点击效果 */
.hero-image-container:active {
    transform: scale(0.98);
}

/* 添加装饰性元素 */
.hero-panel::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(74, 105, 255, 0.1), transparent);
    animation: rotate 20s linear infinite;
    z-index: -1;
}

.hero-panel::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid transparent;
    border-radius: 20px;
    background: linear-gradient(45deg, var(--primary-interactive), #FF6B9D, var(--primary-interactive)) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.hero-panel:hover::after {
    opacity: 0.6;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 添加浮动粒子效果 */
.hero-visual {
    position: relative;
}

.hero-visual::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 157, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(74, 105, 255, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: sparkle 3s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* 增强封面图片的视觉效果 */
.hero-image-container {
    box-shadow:
        0 0 0 1px rgba(255, 255, 255, 0.1),
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.hero-image-container:hover {
    box-shadow:
        0 0 0 1px rgba(74, 105, 255, 0.3),
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(74, 105, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 相册样式 */
.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-heading);
}

.gallery-controls {
    display: flex;
    align-items: center;
    gap: 24px;
}

.view-toggle {
    display: flex;
    background: var(--glass-fill);
    border-radius: 12px;
    padding: 4px;
    backdrop-filter: blur(10px);
}

.toggle-btn {
    background: none;
    border: none;
    color: var(--text-body);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.toggle-btn.active {
    background: var(--primary-interactive);
    color: var(--text-heading);
}

.pagination-info {
    color: var(--text-subtle);
    font-weight: 500;
}

/* 照片网格 */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-smooth);
    background: var(--glass-fill);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-stroke);
    opacity: 0;
    animation: slideInScale 0.6s ease forwards;
    transform: scale(0.8) translateY(20px);
}

@keyframes slideInScale {
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.photo-item:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    border-color: var(--primary-interactive);
}

.photo-item:active {
    transform: translateY(-8px) scale(0.98);
    transition: var(--transition-smooth);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
    filter: brightness(0.9);
}

.photo-item:hover img {
    transform: scale(1.08);
    filter: brightness(1.1);
}

/* 移除了 photo-overlay 相关样式，保持照片显示简洁 */

/* 照片加载状态 */
.photo-item.loading {
    background: var(--glass-fill);
    position: relative;
}

.photo-item.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid var(--glass-stroke);
    border-top-color: var(--primary-interactive);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 照片操作按钮 */
.photo-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-smooth);
}

.photo-item:hover .photo-actions {
    opacity: 1;
    transform: translateY(0);
}

.photo-action-btn {
    width: 36px;
    height: 36px;
    background: var(--glass-fill);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-stroke);
    border-radius: 50%;
    color: var(--text-heading);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    font-size: 14px;
}

.photo-action-btn:hover {
    background: var(--primary-interactive);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(74, 105, 255, 0.3);
}

/* 无照片状态 */
.no-photos {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--text-subtle);
    font-size: 1.125rem;
    background: var(--glass-fill);
    border-radius: 16px;
    border: 1px solid var(--glass-stroke);
    backdrop-filter: blur(10px);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--glass-fill);
    color: var(--text-body);
    border: 1px solid var(--glass-stroke);
    padding: 14px 28px;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition-smooth);
    backdrop-filter: blur(15px);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.pagination-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.pagination-btn:hover:not(:disabled)::before {
    left: 100%;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--glass-hover);
    color: var(--text-heading);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-interactive);
}

.pagination-btn:active:not(:disabled) {
    transform: translateY(0);
}

.pagination-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    filter: grayscale(1);
}

.pagination-btn i {
    transition: transform 0.3s ease;
}

.pagination-btn:hover:not(:disabled) i {
    transform: scale(1.1);
}

.pagination-dots {
    display: flex;
    gap: 12px;
    align-items: center;
}

.pagination-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--glass-stroke);
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
}

.pagination-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--primary-interactive);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.pagination-dot:hover::before {
    width: 20px;
    height: 20px;
    opacity: 0.2;
}

.pagination-dot.active {
    background: var(--primary-interactive);
    transform: scale(1.3);
    box-shadow: 0 0 15px rgba(74, 105, 255, 0.5);
}

.pagination-dot:hover {
    transform: scale(1.2);
}

/* 关于页面 */
.about-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.about-panel {
    padding: 60px;
    max-width: 600px;
    text-align: center;
}

.about-text {
    margin: 32px 0;
    font-size: 1.125rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-interactive);
    margin-bottom: 8px;
}

.stat-label {
    color: var(--text-subtle);
    font-weight: 500;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.active {
    display: flex;
    opacity: 1;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(15px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-close {
    position: absolute;
    top: -60px;
    right: 0;
    background: var(--glass-fill);
    border: 1px solid var(--glass-stroke);
    color: var(--text-heading);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    cursor: pointer;
    backdrop-filter: blur(15px);
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.modal-close:hover {
    background: rgba(255, 0, 0, 0.2);
    border-color: #ff4757;
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.3);
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--glass-fill);
    border: 1px solid var(--glass-stroke);
    color: var(--text-heading);
    width: 56px;
    height: 56px;
    border-radius: 50%;
    cursor: pointer;
    backdrop-filter: blur(15px);
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    opacity: 0.8;
}

.modal-nav:hover {
    background: var(--primary-interactive);
    border-color: var(--primary-interactive);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 25px rgba(74, 105, 255, 0.4);
    opacity: 1;
}

.modal-nav:active {
    transform: translateY(-50%) scale(0.95);
}

.modal-prev {
    left: -80px;
}

.modal-next {
    right: -80px;
}

.modal-prev:hover {
    animation: bounceLeft 0.6s ease;
}

.modal-next:hover {
    animation: bounceRight 0.6s ease;
}

@keyframes bounceLeft {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    50% { transform: translateY(-50%) translateX(-5px); }
}

@keyframes bounceRight {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    50% { transform: translateY(-50%) translateX(5px); }
}

.modal-image-container {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 16px;
    overflow: hidden;
    background: var(--glass-fill);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-stroke);
}

.modal-image-container img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.modal-info {
    margin-top: 20px;
    text-align: center;
    color: var(--text-heading);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.modal-counter {
    color: var(--text-subtle);
}

/* 底部导航 (移动端) */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--glass-fill);
    backdrop-filter: blur(25px);
    border-top: 1px solid var(--glass-stroke);
    display: none;
    justify-content: space-around;
    padding: 8px 0 max(8px, env(safe-area-inset-bottom));
    z-index: 1000;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
    height: auto;
    min-height: 60px;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    color: var(--text-body);
    font-size: 0.7rem;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 12px;
    transition: var(--transition-smooth);
    position: relative;
    min-width: 50px;
    flex: 1;
    max-width: 80px;
}

.bottom-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-interactive), #6B73FF);
    border-radius: 0 0 3px 3px;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.bottom-nav-item.active::before {
    width: 80%;
}

.bottom-nav-item:active {
    transform: scale(0.95);
    background: var(--glass-hover);
}

.bottom-nav-item.active {
    color: var(--primary-interactive);
    background: rgba(74, 105, 255, 0.1);
}

.bottom-nav-item i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.bottom-nav-item.active i {
    transform: scale(1.1);
    filter: drop-shadow(0 0 8px rgba(74, 105, 255, 0.5));
}

.bottom-nav-item span {
    transition: all 0.3s ease;
}

.bottom-nav-item.active span {
    font-weight: 600;
}

/* 触摸反馈和额外交互效果 */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.touch-feedback:active::after {
    width: 200px;
    height: 200px;
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 选择状态样式 */
::selection {
    background: rgba(74, 105, 255, 0.3);
    color: var(--text-heading);
}

::-moz-selection {
    background: rgba(74, 105, 255, 0.3);
    color: var(--text-heading);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--glass-fill);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--glass-stroke);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-interactive);
}

/* 焦点状态 */
button:focus,
.nav-link:focus,
.bottom-nav-item:focus {
    outline: 2px solid var(--primary-interactive);
    outline-offset: 2px;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--glass-stroke);
    border-radius: 50%;
    border-top-color: var(--primary-interactive);
    animation: spin 1s ease-in-out infinite;
}

/* 脉冲动画 */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* 弹跳动画 */
.bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

/* 渐入动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬停发光效果 */
.glow-on-hover {
    transition: all 0.3s ease;
}

.glow-on-hover:hover {
    box-shadow: 0 0 20px rgba(74, 105, 255, 0.5);
    filter: brightness(1.1);
}

/* Toast 动画 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* 图片占位符 */
.image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-subtle);
    background: var(--glass-fill);
    border-radius: 12px;
}

.image-placeholder i {
    font-size: 2rem;
    margin-bottom: 8px;
    opacity: 0.5;
}

.image-placeholder span {
    font-size: 0.875rem;
    opacity: 0.7;
}

/* 视图切换动画 */
.view-toggle {
    position: relative;
}

.view-toggle::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(50% - 4px);
    height: calc(100% - 8px);
    background: var(--primary-interactive);
    border-radius: 8px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

.view-toggle[data-active="list"]::before {
    transform: translateX(100%);
}

.toggle-btn {
    position: relative;
    z-index: 1;
}

/* 增强的悬停效果 */
.enhanced-hover {
    position: relative;
    overflow: hidden;
}

.enhanced-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.enhanced-hover:hover::before {
    left: 100%;
}

/* 脉冲边框效果 */
.pulse-border {
    position: relative;
}

.pulse-border::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--primary-interactive), #6B73FF, var(--primary-interactive));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    animation: pulseBorder 2s ease-in-out infinite;
}

@keyframes pulseBorder {
    0%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.02);
    }
}

/* 磁性效果 */
.magnetic {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic:hover {
    transform: translateY(-2px);
}

/* 3D 翻转效果 */
.flip-card {
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: inherit;
}

.flip-card-back {
    transform: rotateY(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --container-padding: 20px;
        --section-gap: 40px;
    }
    
    .navbar {
        display: none;
    }
    
    .bottom-nav {
        display: flex;
    }
    
    .main-container {
        padding-top: 20px;
        padding-bottom: 70px;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 32px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-panel {
        height: 300px;
        padding: 16px;
    }
    
    .nav-links {
        display: none;
    }
    
    .gallery-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .gallery-controls {
        justify-content: space-between;
    }
    
    .photo-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 16px;
    }
    
    .about-panel {
        padding: 40px 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .modal-nav {
        display: none;
    }
    
    .modal-close {
        top: -40px;
        right: 10px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 16px;
    }
    
    .pagination-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-button {
        width: 100%;
        justify-content: center;
    }
}
