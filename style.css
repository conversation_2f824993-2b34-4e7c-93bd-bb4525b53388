/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色变量 */
    --bg-gradient-start: #2A2A72;
    --bg-gradient-mid: #4C3A9A;
    --bg-gradient-end: #8A2BE2;
    --primary-interactive: #4A69FF;
    --text-heading: #FFFFFF;
    --text-body: #E0E0E0;
    --text-subtle: #B0B0B0;
    --glass-fill: rgba(255, 255, 255, 0.1);
    --glass-stroke: rgba(255, 255, 255, 0.25);
    --glass-hover: rgba(255, 255, 255, 0.15);
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    
    /* 间距 */
    --container-padding: clamp(24px, 5vw, 64px);
    --section-gap: clamp(32px, 8vw, 80px);
    
    /* 动画 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

body {
    font-family: var(--font-family);
    color: var(--text-body);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 动态背景 */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.gradient-mesh {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-mid) 50%, var(--bg-gradient-end) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.decorative-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: float 20s ease-in-out infinite;
}

.blob-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #FF6B9D, #4ECDC4);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.blob-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #A8E6CF, #FFD93D);
    top: 60%;
    right: 15%;
    animation-delay: -7s;
}

.blob-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, #FF8A80, #CE93D8);
    bottom: 20%;
    left: 20%;
    animation-delay: -14s;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* 玻璃拟态效果 */
.glass-panel {
    background: var(--glass-fill);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-stroke);
    border-radius: 20px;
    transition: var(--transition-smooth);
}

.glass-panel:hover {
    background: var(--glass-hover);
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 20px var(--container-padding);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-heading);
}

.nav-logo i {
    font-size: 1.5rem;
    color: var(--primary-interactive);
}

.nav-links {
    display: flex;
    gap: 32px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--text-body);
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 12px;
    transition: var(--transition-smooth);
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-heading);
    background: var(--glass-fill);
    backdrop-filter: blur(10px);
}

.nav-link i {
    font-size: 1rem;
}

/* 主容器 */
.main-container {
    padding-top: 100px;
    padding-bottom: 100px;
    min-height: 100vh;
}

.section {
    display: none;
    padding: var(--section-gap) var(--container-padding);
    max-width: 1200px;
    margin: 0 auto;
}

.section.active {
    display: block;
}

/* 首页样式 */
.hero-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    width: 100%;
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 700;
    color: var(--text-heading);
    margin-bottom: 16px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-subtle);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 24px;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 32px;
    max-width: 500px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: var(--primary-interactive);
    color: var(--text-heading);
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    box-shadow: 0 8px 32px rgba(74, 105, 255, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(74, 105, 255, 0.4);
}

.hero-panel {
    padding: 40px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    width: 100%;
    height: 100%;
}

.preview-item {
    background: var(--glass-fill);
    border-radius: 12px;
    border: 1px solid var(--glass-stroke);
    animation: pulse 3s ease-in-out infinite;
}

.preview-item:nth-child(1) { animation-delay: 0s; }
.preview-item:nth-child(2) { animation-delay: 0.5s; }
.preview-item:nth-child(3) { animation-delay: 1s; }
.preview-item:nth-child(4) { animation-delay: 1.5s; }

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 相册样式 */
.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-heading);
}

.gallery-controls {
    display: flex;
    align-items: center;
    gap: 24px;
}

.view-toggle {
    display: flex;
    background: var(--glass-fill);
    border-radius: 12px;
    padding: 4px;
    backdrop-filter: blur(10px);
}

.toggle-btn {
    background: none;
    border: none;
    color: var(--text-body);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.toggle-btn.active {
    background: var(--primary-interactive);
    color: var(--text-heading);
}

.pagination-info {
    color: var(--text-subtle);
    font-weight: 500;
}

/* 照片网格 */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-smooth);
    background: var(--glass-fill);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-stroke);
}

.photo-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: var(--text-heading);
    padding: 20px;
    transform: translateY(100%);
    transition: var(--transition-smooth);
}

.photo-item:hover .photo-overlay {
    transform: translateY(0);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--glass-fill);
    color: var(--text-body);
    border: 1px solid var(--glass-stroke);
    padding: 12px 24px;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition-smooth);
    backdrop-filter: blur(10px);
}

.pagination-btn:hover:not(:disabled) {
    background: var(--glass-hover);
    color: var(--text-heading);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-dots {
    display: flex;
    gap: 8px;
}

.pagination-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--glass-stroke);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.pagination-dot.active {
    background: var(--primary-interactive);
    transform: scale(1.2);
}

/* 关于页面 */
.about-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.about-panel {
    padding: 60px;
    max-width: 600px;
    text-align: center;
}

.about-text {
    margin: 32px 0;
    font-size: 1.125rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-interactive);
    margin-bottom: 8px;
}

.stat-label {
    color: var(--text-subtle);
    font-weight: 500;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.modal-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: var(--glass-fill);
    border: 1px solid var(--glass-stroke);
    color: var(--text-heading);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--glass-fill);
    border: 1px solid var(--glass-stroke);
    color: var(--text-heading);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
}

.modal-prev { left: -70px; }
.modal-next { right: -70px; }

.modal-image-container {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 16px;
    overflow: hidden;
    background: var(--glass-fill);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-stroke);
}

.modal-image-container img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.modal-info {
    margin-top: 20px;
    text-align: center;
    color: var(--text-heading);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.modal-counter {
    color: var(--text-subtle);
}

/* 底部导航 (移动端) */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--glass-fill);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--glass-stroke);
    display: none;
    justify-content: space-around;
    padding: 12px 0;
    z-index: 1000;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    color: var(--text-body);
    font-size: 0.75rem;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 12px;
    transition: var(--transition-smooth);
}

.bottom-nav-item.active {
    color: var(--primary-interactive);
}

.bottom-nav-item i {
    font-size: 1.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --container-padding: 20px;
        --section-gap: 40px;
    }
    
    .navbar {
        display: none;
    }
    
    .bottom-nav {
        display: flex;
    }
    
    .main-container {
        padding-top: 20px;
        padding-bottom: 80px;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .nav-links {
        display: none;
    }
    
    .gallery-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .gallery-controls {
        justify-content: space-between;
    }
    
    .photo-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 16px;
    }
    
    .about-panel {
        padding: 40px 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .modal-nav {
        display: none;
    }
    
    .modal-close {
        top: -40px;
        right: 10px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 16px;
    }
    
    .pagination-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-button {
        width: 100%;
        justify-content: center;
    }
}
