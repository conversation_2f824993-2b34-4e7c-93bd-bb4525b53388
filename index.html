<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小宝的照片集 - Aura Gallery</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 动态背景 -->
    <div class="background-container">
        <div class="gradient-mesh"></div>
        <div class="decorative-blob blob-1"></div>
        <div class="decorative-blob blob-2"></div>
        <div class="decorative-blob blob-3"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-camera-retro"></i>
                <span>小宝的照片集</span>
            </div>
            <div class="nav-links">
                <a href="#home" class="nav-link active" data-section="home">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#gallery" class="nav-link" data-section="gallery">
                    <i class="fas fa-images"></i>
                    <span>相册</span>
                </a>
                <a href="#about" class="nav-link" data-section="about">
                    <i class="fas fa-heart"></i>
                    <span>关于</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- 主要内容容器 -->
    <main class="main-container">
        <!-- 首页部分 -->
        <section id="home" class="section active">
            <div class="hero-section">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">小宝的美好时光</h1>
                        <p class="hero-subtitle">记录每一个珍贵瞬间</p>
                        <p class="hero-description">
                            在这里，每一张照片都承载着美好的回忆，
                            每一个瞬间都值得被珍藏。让我们一起回顾这些温馨的时光。
                        </p>
                        <button class="cta-button touch-feedback glow-on-hover" onclick="showSection('gallery')">
                            <i class="fas fa-images"></i>
                            浏览相册
                        </button>
                    </div>
                    <div class="hero-visual">
                        <div class="glass-panel hero-panel">
                            <div class="preview-grid">
                                <div class="preview-item"></div>
                                <div class="preview-item"></div>
                                <div class="preview-item"></div>
                                <div class="preview-item"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 相册部分 -->
        <section id="gallery" class="section">
            <div class="gallery-header">
                <h2 class="section-title">照片相册</h2>
                <div class="gallery-controls">
                    <div class="view-toggle">
                        <button class="toggle-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="toggle-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    <div class="pagination-info">
                        <span id="current-page">1</span> / <span id="total-pages">1</span>
                    </div>
                </div>
            </div>
            
            <div class="gallery-container">
                <div id="photo-grid" class="photo-grid"></div>
            </div>
            
            <div class="pagination">
                <button id="prev-btn" class="pagination-btn" disabled>
                    <i class="fas fa-chevron-left"></i>
                    上一页
                </button>
                <div class="pagination-dots" id="pagination-dots"></div>
                <button id="next-btn" class="pagination-btn">
                    下一页
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

        <!-- 关于部分 -->
        <section id="about" class="section">
            <div class="about-content">
                <div class="glass-panel about-panel">
                    <h2 class="section-title">关于小宝</h2>
                    <div class="about-text">
                        <p>这里收录了小宝成长过程中的珍贵照片，每一张都记录着独特的瞬间和美好的回忆。</p>
                        <p>通过这个相册，我们可以一起回顾那些温馨的时光，感受成长的足迹。</p>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" id="total-photos">0</div>
                            <div class="stat-label">张照片</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">∞</div>
                            <div class="stat-label">美好回忆</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">♥</div>
                            <div class="stat-label">满满的爱</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 照片查看器模态框 -->
    <div id="photo-modal" class="modal">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">
                <i class="fas fa-times"></i>
            </button>
            <button class="modal-nav modal-prev" onclick="navigateModal(-1)">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="modal-nav modal-next" onclick="navigateModal(1)">
                <i class="fas fa-chevron-right"></i>
            </button>
            <div class="modal-image-container">
                <img id="modal-image" src="" alt="">
            </div>
            <div class="modal-info">
                <div class="modal-title" id="modal-title"></div>
                <div class="modal-counter">
                    <span id="modal-current">1</span> / <span id="modal-total">1</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 (移动端) -->
    <nav class="bottom-nav">
        <a href="#home" class="bottom-nav-item active" data-section="home">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="#gallery" class="bottom-nav-item" data-section="gallery">
            <i class="fas fa-images"></i>
            <span>相册</span>
        </a>
        <a href="#about" class="bottom-nav-item" data-section="about">
            <i class="fas fa-heart"></i>
            <span>关于</span>
        </a>
    </nav>

    <script src="script.js"></script>
</body>
</html>
