# 🚀 一键部署到 GitHub Pages 指南

## 📋 准备工作

1. **注册 GitHub 账号** (如果还没有)
   - 访问 [github.com](https://github.com) 注册账号

2. **安装 Git** (如果还没有)
   - Windows: 下载 [Git for Windows](https://git-scm.com/download/win)
   - macOS: `brew install git` 或从官网下载
   - Linux: `sudo apt install git` 或 `sudo yum install git`

## 🎯 一键部署步骤

### 方法一：使用部署脚本 (推荐)

#### Windows 用户：
1. 双击运行 `deploy.bat` 文件
2. 按照提示操作即可

#### macOS/Linux 用户：
1. 打开终端，进入项目目录
2. 运行：`./deploy.sh`
3. 按照提示操作即可

### 方法二：手动部署

1. **创建 GitHub 仓库**
   ```bash
   # 在 GitHub 网站上创建新仓库，记住仓库地址
   ```

2. **初始化并推送代码**
   ```bash
   # 初始化 Git 仓库
   git init
   
   # 添加所有文件
   git add .
   
   # 提交更改
   git commit -m "Initial commit: 小宝的照片集"
   
   # 添加远程仓库 (替换为你的仓库地址)
   git remote add origin https://github.com/你的用户名/仓库名.git
   
   # 推送到 GitHub
   git branch -M main
   git push -u origin main
   ```

3. **启用 GitHub Pages**
   - 访问你的 GitHub 仓库
   - 点击 **Settings** 选项卡
   - 在左侧菜单中找到 **Pages**
   - 在 **Source** 下选择 **"GitHub Actions"**
   - 等待几分钟，网站就会自动部署！

## 🌐 访问你的网站

部署完成后，你的网站地址将是：
```
https://你的用户名.github.io/仓库名
```

例如：`https://xiaoming.github.io/xiaobao-gallery`

## 🔄 更新网站

当你想更新照片或修改网站时：

```bash
# 添加新的更改
git add .

# 提交更改
git commit -m "更新照片"

# 推送到 GitHub
git push
```

网站会自动重新部署！

## 📁 添加新照片

1. 将新照片放入 `image/` 文件夹
2. 编辑 `script.js` 文件，在 `imageFiles` 数组中添加新照片的文件名
3. 提交并推送更改

## 🎨 自定义网站

### 修改标题和内容
- 编辑 `index.html` 文件中的文本

### 更改颜色主题
- 编辑 `style.css` 文件中的 CSS 变量

### 替换首页封面
- 替换 `index.html` 中的图片路径

## 🆘 常见问题

### Q: 网站显示 404 错误
A: 确保在 GitHub 仓库设置中正确启用了 Pages，并选择了 "GitHub Actions" 作为源。

### Q: 照片不显示
A: 检查图片文件路径是否正确，确保图片在 `image/` 文件夹中。

### Q: 部署失败
A: 检查 GitHub Actions 页面的错误日志，通常是文件路径或权限问题。

### Q: 想要自定义域名
A: 在仓库根目录创建 `CNAME` 文件，内容为你的域名。

## 📞 技术支持

如果遇到问题，可以：
1. 查看 GitHub Actions 的部署日志
2. 检查浏览器控制台的错误信息
3. 确保所有文件路径正确

---

🎉 **恭喜！你的照片网站即将上线！**
